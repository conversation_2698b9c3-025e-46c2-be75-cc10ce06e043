class_name HandEvaluator
extends RefCounted

## Evaluates poker hands and determines hand rankings
## Supports all standard poker hand types from high card to royal flush

enum HandRank {
	HIGH_CARD = 1,
	PAIR = 2,
	TWO_PAIR = 3,
	THREE_OF_A_KIND = 4,
	STRAIGHT = 5,
	FLUSH = 6,
	FULL_HOUSE = 7,
	FOUR_OF_A_KIND = 8,
	STRAIGHT_FLUSH = 9,
	ROYAL_FLUSH = 10
}

class HandResult:
	var rank: HandRank
	var primary_value: int  # Main ranking value (e.g., pair rank)
	var secondary_value: int  # Secondary ranking value (e.g., kicker)
	var tertiary_value: int  # Third ranking value
	var quaternary_value: int  # Fourth ranking value
	var quinary_value: int  # Fifth ranking value
	var hand_cards: Array[PokerCard]  # The 5 cards that make the best hand

	func _init(hand_rank: HandRank, cards: Array[PokerCard]):
		rank = hand_rank
		hand_cards = cards.duplicate()
		_calculate_values()

	func _calculate_values() -> void:
		# Sort cards by rank (descending)
		hand_cards.sort_custom(func(a, b): return a.rank > b.rank)

		match rank:
			HandRank.HIGH_CARD:
				_set_high_card_values()
			HandRank.PAIR:
				_set_pair_values()
			HandRank.TWO_PAIR:
				_set_two_pair_values()
			HandRank.THREE_OF_A_KIND:
				_set_three_of_a_kind_values()
			HandRank.STRAIGHT:
				_set_straight_values()
			HandRank.FLUSH:
				_set_flush_values()
			HandRank.FULL_HOUSE:
				_set_full_house_values()
			HandRank.FOUR_OF_A_KIND:
				_set_four_of_a_kind_values()
			HandRank.STRAIGHT_FLUSH, HandRank.ROYAL_FLUSH:
				_set_straight_flush_values()

	func _set_high_card_values() -> void:
		primary_value = hand_cards[0].rank
		secondary_value = hand_cards[1].rank
		tertiary_value = hand_cards[2].rank
		quaternary_value = hand_cards[3].rank
		quinary_value = hand_cards[4].rank

	func _set_pair_values() -> void:
		var rank_counts = _get_rank_counts()
		for rank_value in rank_counts:
			if rank_counts[rank_value] == 2:
				primary_value = rank_value
				break

		var kickers = []
		for card in hand_cards:
			if card.rank != primary_value:
				kickers.append(card.rank)
		kickers.sort()
		kickers.reverse()

		secondary_value = kickers[0] if kickers.size() > 0 else 0
		tertiary_value = kickers[1] if kickers.size() > 1 else 0
		quaternary_value = kickers[2] if kickers.size() > 2 else 0

	func _set_two_pair_values() -> void:
		var rank_counts = _get_rank_counts()
		var pairs = []
		for rank_value in rank_counts:
			if rank_counts[rank_value] == 2:
				pairs.append(rank_value)
		pairs.sort()
		pairs.reverse()

		primary_value = pairs[0]
		secondary_value = pairs[1]

		# Find kicker
		for card in hand_cards:
			if card.rank != primary_value and card.rank != secondary_value:
				tertiary_value = card.rank
				break

	func _set_three_of_a_kind_values() -> void:
		var rank_counts = _get_rank_counts()
		for rank_value in rank_counts:
			if rank_counts[rank_value] == 3:
				primary_value = rank_value
				break

		var kickers = []
		for card in hand_cards:
			if card.rank != primary_value:
				kickers.append(card.rank)
		kickers.sort()
		kickers.reverse()

		secondary_value = kickers[0] if kickers.size() > 0 else 0
		tertiary_value = kickers[1] if kickers.size() > 1 else 0

	func _set_straight_values() -> void:
		# Check for A-2-3-4-5 straight (wheel)
		if hand_cards[0].rank == PokerCard.Rank.ACE and hand_cards[1].rank == PokerCard.Rank.FIVE:
			primary_value = PokerCard.Rank.FIVE  # Wheel straight is ranked by the 5
		else:
			primary_value = hand_cards[0].rank  # Highest card in straight

	func _set_flush_values() -> void:
		_set_high_card_values()  # Same as high card for comparison

	func _set_full_house_values() -> void:
		var rank_counts = _get_rank_counts()
		for rank_value in rank_counts:
			if rank_counts[rank_value] == 3:
				primary_value = rank_value
			elif rank_counts[rank_value] == 2:
				secondary_value = rank_value

	func _set_four_of_a_kind_values() -> void:
		var rank_counts = _get_rank_counts()
		for rank_value in rank_counts:
			if rank_counts[rank_value] == 4:
				primary_value = rank_value
			else:
				secondary_value = rank_value

	func _set_straight_flush_values() -> void:
		_set_straight_values()  # Same logic as straight

	func _get_rank_counts() -> Dictionary:
		var counts = {}
		for card in hand_cards:
			if card.rank in counts:
				counts[card.rank] += 1
			else:
				counts[card.rank] = 1
		return counts

	func get_hand_name() -> String:
		match rank:
			HandRank.HIGH_CARD:
				return "High Card"
			HandRank.PAIR:
				return "Pair"
			HandRank.TWO_PAIR:
				return "Two Pair"
			HandRank.THREE_OF_A_KIND:
				return "Three of a Kind"
			HandRank.STRAIGHT:
				return "Straight"
			HandRank.FLUSH:
				return "Flush"
			HandRank.FULL_HOUSE:
				return "Full House"
			HandRank.FOUR_OF_A_KIND:
				return "Four of a Kind"
			HandRank.STRAIGHT_FLUSH:
				return "Straight Flush"
			HandRank.ROYAL_FLUSH:
				return "Royal Flush"
			_:
				return "Unknown"

## Main evaluation function - finds the best 5-card hand from 7 cards
static func evaluate_hand(cards: Array[PokerCard]) -> HandResult:
	if cards.size() < 5:
		push_error("Need at least 5 cards to evaluate hand")
		return null

	var best_hand = null
	var best_rank = HandRank.HIGH_CARD

	# Generate all possible 5-card combinations from the available cards
	var combinations = _generate_combinations(cards, 5)

	for combo in combinations:
		var hand_result = _evaluate_five_cards(combo)
		if hand_result.rank > best_rank or (hand_result.rank == best_rank and _is_better_hand(hand_result, best_hand)):
			best_hand = hand_result
			best_rank = hand_result.rank

	return best_hand

## Evaluate exactly 5 cards
static func _evaluate_five_cards(cards: Array[PokerCard]) -> HandResult:
	# Sort cards by rank (descending)
	var sorted_cards = cards.duplicate()
	sorted_cards.sort_custom(func(a, b): return a.rank > b.rank)

	# Check for each hand type (from highest to lowest)
	if _is_royal_flush(sorted_cards):
		return HandResult.new(HandRank.ROYAL_FLUSH, sorted_cards)
	elif _is_straight_flush(sorted_cards):
		return HandResult.new(HandRank.STRAIGHT_FLUSH, sorted_cards)
	elif _is_four_of_a_kind(sorted_cards):
		return HandResult.new(HandRank.FOUR_OF_A_KIND, sorted_cards)
	elif _is_full_house(sorted_cards):
		return HandResult.new(HandRank.FULL_HOUSE, sorted_cards)
	elif _is_flush(sorted_cards):
		return HandResult.new(HandRank.FLUSH, sorted_cards)
	elif _is_straight(sorted_cards):
		return HandResult.new(HandRank.STRAIGHT, sorted_cards)
	elif _is_three_of_a_kind(sorted_cards):
		return HandResult.new(HandRank.THREE_OF_A_KIND, sorted_cards)
	elif _is_two_pair(sorted_cards):
		return HandResult.new(HandRank.TWO_PAIR, sorted_cards)
	elif _is_pair(sorted_cards):
		return HandResult.new(HandRank.PAIR, sorted_cards)
	else:
		return HandResult.new(HandRank.HIGH_CARD, sorted_cards)

## Check if hand is a royal flush (A-K-Q-J-10 of same suit)
static func _is_royal_flush(cards: Array[PokerCard]) -> bool:
	if not _is_flush(cards):
		return false

	var ranks = []
	for card in cards:
		ranks.append(card.rank)
	ranks.sort()

	return ranks == [PokerCard.Rank.TEN, PokerCard.Rank.JACK, PokerCard.Rank.QUEEN, PokerCard.Rank.KING, PokerCard.Rank.ACE]

## Check if hand is a straight flush
static func _is_straight_flush(cards: Array[PokerCard]) -> bool:
	return _is_flush(cards) and _is_straight(cards)

## Check if hand is four of a kind
static func _is_four_of_a_kind(cards: Array[PokerCard]) -> bool:
	var rank_counts = _get_rank_counts_static(cards)
	return 4 in rank_counts.values()

## Check if hand is a full house
static func _is_full_house(cards: Array[PokerCard]) -> bool:
	var rank_counts = _get_rank_counts_static(cards)
	return 3 in rank_counts.values() and 2 in rank_counts.values()

## Check if hand is a flush
static func _is_flush(cards: Array[PokerCard]) -> bool:
	var suit = cards[0].suit
	for card in cards:
		if card.suit != suit:
			return false
	return true

## Check if hand is a straight
static func _is_straight(cards: Array[PokerCard]) -> bool:
	var ranks = []
	for card in cards:
		ranks.append(card.rank)
	ranks.sort()

	# Check for regular straight
	for i in range(1, ranks.size()):
		if ranks[i] != ranks[i-1] + 1:
			# Check for A-2-3-4-5 straight (wheel)
			if ranks == [PokerCard.Rank.TWO, PokerCard.Rank.THREE, PokerCard.Rank.FOUR, PokerCard.Rank.FIVE, PokerCard.Rank.ACE]:
				return true
			return false
	return true

## Check if hand is three of a kind
static func _is_three_of_a_kind(cards: Array[PokerCard]) -> bool:
	var rank_counts = _get_rank_counts_static(cards)
	return 3 in rank_counts.values()

## Check if hand is two pair
static func _is_two_pair(cards: Array[PokerCard]) -> bool:
	var rank_counts = _get_rank_counts_static(cards)
	var pair_count = 0
	for count in rank_counts.values():
		if count == 2:
			pair_count += 1
	return pair_count == 2

## Check if hand is a pair
static func _is_pair(cards: Array[PokerCard]) -> bool:
	var rank_counts = _get_rank_counts_static(cards)
	return 2 in rank_counts.values()

## Get rank counts for static methods
static func _get_rank_counts_static(cards: Array[PokerCard]) -> Dictionary:
	var counts = {}
	for card in cards:
		if card.rank in counts:
			counts[card.rank] += 1
		else:
			counts[card.rank] = 1
	return counts

## Compare two hands to determine which is better
static func _is_better_hand(hand1: HandResult, hand2: HandResult) -> bool:
	if hand1.rank != hand2.rank:
		return hand1.rank > hand2.rank

	# Same rank, compare values
	if hand1.primary_value != hand2.primary_value:
		return hand1.primary_value > hand2.primary_value
	if hand1.secondary_value != hand2.secondary_value:
		return hand1.secondary_value > hand2.secondary_value
	if hand1.tertiary_value != hand2.tertiary_value:
		return hand1.tertiary_value > hand2.tertiary_value
	if hand1.quaternary_value != hand2.quaternary_value:
		return hand1.quaternary_value > hand2.quaternary_value
	if hand1.quinary_value != hand2.quinary_value:
		return hand1.quinary_value > hand2.quinary_value

	return false  # Hands are equal

## Generate all combinations of k cards from n cards
static func _generate_combinations(cards: Array[PokerCard], k: int) -> Array:
	var result = []
	_generate_combinations_recursive(cards, k, 0, [], result)
	return result

## Recursive helper for combination generation
static func _generate_combinations_recursive(cards: Array[PokerCard], k: int, start: int, current: Array, result: Array) -> void:
	if current.size() == k:
		result.append(current.duplicate())
		return

	for i in range(start, cards.size()):
		current.append(cards[i])
		_generate_combinations_recursive(cards, k, i + 1, current, result)
		current.pop_back()

## Compare two HandResults (returns -1, 0, or 1)
static func compare_hands(hand1: HandResult, hand2: HandResult) -> int:
	if _is_better_hand(hand1, hand2):
		return 1
	elif _is_better_hand(hand2, hand1):
		return -1
	else:
		return 0

## Get a detailed description of the hand
static func get_hand_description(hand: HandResult) -> String:
	var description = hand.get_hand_name()

	match hand.rank:
		HandRank.PAIR:
			description += " of " + _rank_to_string(hand.primary_value) + "s"
		HandRank.TWO_PAIR:
			description += ", " + _rank_to_string(hand.primary_value) + "s and " + _rank_to_string(hand.secondary_value) + "s"
		HandRank.THREE_OF_A_KIND:
			description += ", " + _rank_to_string(hand.primary_value) + "s"
		HandRank.STRAIGHT:
			description += ", " + _rank_to_string(hand.primary_value) + " high"
		HandRank.FLUSH:
			description += ", " + _rank_to_string(hand.primary_value) + " high"
		HandRank.FULL_HOUSE:
			description += ", " + _rank_to_string(hand.primary_value) + "s full of " + _rank_to_string(hand.secondary_value) + "s"
		HandRank.FOUR_OF_A_KIND:
			description += ", " + _rank_to_string(hand.primary_value) + "s"
		HandRank.STRAIGHT_FLUSH:
			description += ", " + _rank_to_string(hand.primary_value) + " high"

	return description

## Convert rank to string for descriptions
static func _rank_to_string(rank: int) -> String:
	match rank:
		PokerCard.Rank.TWO:
			return "Two"
		PokerCard.Rank.THREE:
			return "Three"
		PokerCard.Rank.FOUR:
			return "Four"
		PokerCard.Rank.FIVE:
			return "Five"
		PokerCard.Rank.SIX:
			return "Six"
		PokerCard.Rank.SEVEN:
			return "Seven"
		PokerCard.Rank.EIGHT:
			return "Eight"
		PokerCard.Rank.NINE:
			return "Nine"
		PokerCard.Rank.TEN:
			return "Ten"
		PokerCard.Rank.JACK:
			return "Jack"
		PokerCard.Rank.QUEEN:
			return "Queen"
		PokerCard.Rank.KING:
			return "King"
		PokerCard.Rank.ACE:
			return "Ace"
		_:
			return "Unknown"