class_name PokerCardFactory
extends JsonCardFactory

## A specialized card factory for poker games that creates PokerCard instances
## Extends the existing JsonCardFactory to work with our poker system

## Override to create PokerCard instances instead of regular Card instances
func create_card(card_name: String, target: CardContainer) -> Card:
	var card_data = _get_card_data(card_name)
	if card_data.is_empty():
		push_error("Card data not found for: " + card_name)
		return null

	# Create PokerCard instance
	var poker_card = PokerCard.new()

	# Set basic card properties
	poker_card.card_name = card_name
	poker_card.card_size = card_manager.card_size
	poker_card.card_info = card_data

	# Set poker-specific properties
	_set_poker_properties(poker_card, card_data)

	# Set textures
	var front_texture = _load_texture(card_data.get("front_image", ""))
	poker_card.front_image = front_texture
	poker_card.back_image = card_manager.back_image

	# Add to target container
	if target:
		target.add_child(poker_card)
		poker_card.card_container = target

	return poker_card

## Set poker-specific properties from card data
func _set_poker_properties(poker_card: PokerCard, card_data: Dictionary) -> void:
	var suit_string = card_data.get("suit", "")
	var value_string = card_data.get("value", "")

	# Convert suit string to enum
	var suit = _string_to_suit(suit_string)
	var rank = _string_to_rank(value_string)

	poker_card.initialize_poker_card(suit, rank)

## Convert suit string to PokerCard.Suit enum
func _string_to_suit(suit_string: String) -> PokerCard.Suit:
	match suit_string.to_lower():
		"club", "clubs":
			return PokerCard.Suit.CLUBS
		"diamond", "diamonds":
			return PokerCard.Suit.DIAMONDS
		"heart", "hearts":
			return PokerCard.Suit.HEARTS
		"spade", "spades":
			return PokerCard.Suit.SPADES
		_:
			push_error("Unknown suit: " + suit_string)
			return PokerCard.Suit.CLUBS

## Convert value string to PokerCard.Rank enum
func _string_to_rank(value_string: String) -> PokerCard.Rank:
	match value_string.to_upper():
		"2":
			return PokerCard.Rank.TWO
		"3":
			return PokerCard.Rank.THREE
		"4":
			return PokerCard.Rank.FOUR
		"5":
			return PokerCard.Rank.FIVE
		"6":
			return PokerCard.Rank.SIX
		"7":
			return PokerCard.Rank.SEVEN
		"8":
			return PokerCard.Rank.EIGHT
		"9":
			return PokerCard.Rank.NINE
		"10":
			return PokerCard.Rank.TEN
		"J", "JACK":
			return PokerCard.Rank.JACK
		"Q", "QUEEN":
			return PokerCard.Rank.QUEEN
		"K", "KING":
			return PokerCard.Rank.KING
		"A", "ACE":
			return PokerCard.Rank.ACE
		_:
			push_error("Unknown rank: " + value_string)
			return PokerCard.Rank.TWO

## Helper method to get card data
func _get_card_data(card_name: String) -> Dictionary:
	if card_name in card_data_cache:
		return card_data_cache[card_name]

	# Try to load from file if not cached
	var file_path = card_manager.card_info_dir + "/" + card_name + ".json"
	if FileAccess.file_exists(file_path):
		var file = FileAccess.open(file_path, FileAccess.READ)
		if file:
			var json_string = file.get_as_text()
			file.close()

			var json = JSON.new()
			var parse_result = json.parse(json_string)
			if parse_result == OK:
				var data = json.data
				card_data_cache[card_name] = data
				return data

	return {}

## Helper method to load texture
func _load_texture(image_path: String) -> Texture2D:
	if image_path.is_empty():
		return null

	var full_path = card_manager.card_asset_dir + "/" + image_path
	return load(full_path) as Texture2D