class_name PokerDeck
extends RefCounted

## A standard 52-card poker deck with shuffling and dealing functionality
## Uses Fisher-Yates shuffle algorithm for proper randomization

var cards: Array[PokerCard] = []
var dealt_cards: Array[PokerCard] = []
var rng: RandomNumberGenerator

## Initialize the deck with all 52 cards
func _init():
	rng = RandomNumberGenerator.new()
	rng.randomize()
	_create_standard_deck()

## Create a standard 52-card deck
func _create_standard_deck() -> void:
	cards.clear()
	dealt_cards.clear()

	# Create all 52 cards (4 suits × 13 ranks)
	for suit in PokerCard.Suit.values():
		for rank in range(PokerCard.Rank.TWO, PokerCard.Rank.ACE + 1):
			var card = PokerCard.new()
			card.initialize_poker_card(suit, rank)
			cards.append(card)

## Shuffle the deck using Fisher-Yates algorithm
func shuffle() -> void:
	for i in range(cards.size() - 1, 0, -1):
		var j = rng.randi_range(0, i)
		var temp = cards[i]
		cards[i] = cards[j]
		cards[j] = temp

## Deal a single card from the top of the deck
func deal_card() -> PokerCard:
	if cards.is_empty():
		push_error("Cannot deal from empty deck!")
		return null

	var card = cards.pop_back()
	dealt_cards.append(card)
	return card

## Deal multiple cards
func deal_cards(count: int) -> Array[PokerCard]:
	var dealt: Array[PokerCard] = []
	for i in count:
		var card = deal_card()
		if card:
			dealt.append(card)
		else:
			break
	return dealt

## Return all dealt cards to the deck and shuffle
func reset_and_shuffle() -> void:
	# Return all dealt cards to the deck
	for card in dealt_cards:
		cards.append(card)
	dealt_cards.clear()
	shuffle()

## Get the number of cards remaining in the deck
func cards_remaining() -> int:
	return cards.size()

## Get the number of cards that have been dealt
func cards_dealt() -> int:
	return dealt_cards.size()

## Check if the deck is empty
func is_empty() -> bool:
	return cards.is_empty()

## Peek at the top card without dealing it
func peek_top_card() -> PokerCard:
	if cards.is_empty():
		return null
	return cards.back()

## Get all cards in the deck (for debugging)
func get_all_cards() -> Array[PokerCard]:
	return cards.duplicate()

## Get all dealt cards (for debugging)
func get_dealt_cards() -> Array[PokerCard]:
	return dealt_cards.duplicate()

## Set a custom seed for reproducible shuffles (useful for testing)
func set_seed(seed_value: int) -> void:
	rng.seed = seed_value

## Get a string representation of the deck state
func to_string() -> String:
	return "Deck: %d cards remaining, %d cards dealt" % [cards_remaining(), cards_dealt()]