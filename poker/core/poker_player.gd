class_name PokerPlayer
extends RefCounted

## Represents a poker player with chips, cards, and betting actions
## Handles player state management for Texas Hold'em

enum PlayerAction {
	FOLD = 0,
	CHECK = 1,
	CALL = 2,
	RAISE = 3,
	ALL_IN = 4
}

enum PlayerState {
	WAITING = 0,      # Waiting for game to start
	ACTIVE = 1,       # Active in current hand
	FOLDED = 2,       # Folded current hand
	ALL_IN = 3,       # All-in
	SITTING_OUT = 4   # Sitting out
}

## Player identification
@export var player_id: int
@export var player_name: String
@export var is_human: bool = true

## Chip management
@export var chips: int = 1000
@export var current_bet: int = 0
@export var total_bet_this_hand: int = 0

## Player state
var state: PlayerState = PlayerState.WAITING
var hole_cards: Array[PokerCard] = []
var seat_position: int = -1
var is_dealer: bool = false
var is_small_blind: bool = false
var is_big_blind: bool = false

## Action tracking
var last_action: PlayerAction
var last_bet_amount: int = 0
var has_acted_this_round: bool = false

## Statistics (optional for AI and analysis)
var hands_played: int = 0
var hands_won: int = 0
var total_winnings: int = 0

signal action_taken(player: PokerPlayer, action: PlayerAction, amount: int)
signal chips_changed(player: PokerPlayer, new_amount: int)

func _init(id: int = 0, name: String = "", starting_chips: int = 1000):
	player_id = id
	player_name = name
	chips = starting_chips

## Reset player for new hand
func reset_for_new_hand() -> void:
	hole_cards.clear()
	current_bet = 0
	total_bet_this_hand = 0
	state = PlayerState.ACTIVE if chips > 0 else PlayerState.SITTING_OUT
	has_acted_this_round = false
	is_dealer = false
	is_small_blind = false
	is_big_blind = false

## Deal hole cards to player
func deal_hole_cards(cards: Array[PokerCard]) -> void:
	hole_cards = cards.duplicate()

## Get hole cards
func get_hole_cards() -> Array[PokerCard]:
	return hole_cards.duplicate()

## Check if player can act
func can_act() -> bool:
	return state == PlayerState.ACTIVE and chips > 0

## Perform a betting action
func perform_action(action: PlayerAction, amount: int = 0) -> bool:
	if not can_act():
		return false

	match action:
		PlayerAction.FOLD:
			return _fold()
		PlayerAction.CHECK:
			return _check()
		PlayerAction.CALL:
			return _call(amount)
		PlayerAction.RAISE:
			return _raise(amount)
		PlayerAction.ALL_IN:
			return _all_in()

	return false

## Fold action
func _fold() -> bool:
	state = PlayerState.FOLDED
	last_action = PlayerAction.FOLD
	has_acted_this_round = true
	action_taken.emit(self, PlayerAction.FOLD, 0)
	return true

## Check action
func _check() -> bool:
	last_action = PlayerAction.CHECK
	has_acted_this_round = true
	action_taken.emit(self, PlayerAction.CHECK, 0)
	return true

## Call action
func _call(amount: int) -> bool:
	var call_amount = min(amount - current_bet, chips)
	if call_amount < 0:
		call_amount = 0

	_place_bet(call_amount)

	if chips == 0:
		state = PlayerState.ALL_IN
		last_action = PlayerAction.ALL_IN
	else:
		last_action = PlayerAction.CALL

	has_acted_this_round = true
	action_taken.emit(self, last_action, call_amount)
	return true

## Raise action
func _raise(total_amount: int) -> bool:
	var raise_amount = total_amount - current_bet
	if raise_amount > chips:
		return _all_in()

	if raise_amount <= 0:
		return false

	_place_bet(raise_amount)
	last_action = PlayerAction.RAISE
	last_bet_amount = total_amount
	has_acted_this_round = true
	action_taken.emit(self, PlayerAction.RAISE, total_amount)
	return true

## All-in action
func _all_in() -> bool:
	var all_in_amount = chips
	_place_bet(all_in_amount)
	state = PlayerState.ALL_IN
	last_action = PlayerAction.ALL_IN
	has_acted_this_round = true
	action_taken.emit(self, PlayerAction.ALL_IN, current_bet)
	return true

## Place a bet (internal method)
func _place_bet(amount: int) -> void:
	var actual_amount = min(amount, chips)
	chips -= actual_amount
	current_bet += actual_amount
	total_bet_this_hand += actual_amount
	chips_changed.emit(self, chips)

## Add chips to player
func add_chips(amount: int) -> void:
	chips += amount
	chips_changed.emit(self, chips)

## Remove chips from player
func remove_chips(amount: int) -> bool:
	if amount > chips:
		return false
	chips -= amount
	chips_changed.emit(self, chips)
	return true

## Reset betting for new round
func reset_for_new_round() -> void:
	current_bet = 0
	has_acted_this_round = false
	if state == PlayerState.ACTIVE:
		# Player remains active for next round
		pass

## Get available actions for this player
func get_available_actions(current_bet_to_call: int) -> Array[PlayerAction]:
	var actions: Array[PlayerAction] = []

	if not can_act():
		return actions

	# Can always fold
	actions.append(PlayerAction.FOLD)

	# Check if no bet to call
	if current_bet_to_call == current_bet:
		actions.append(PlayerAction.CHECK)
	else:
		# Can call if have enough chips
		if chips >= (current_bet_to_call - current_bet):
			actions.append(PlayerAction.CALL)

	# Can raise if have enough chips for minimum raise
	if chips > (current_bet_to_call - current_bet):
		actions.append(PlayerAction.RAISE)

	# Can always go all-in if have chips
	if chips > 0:
		actions.append(PlayerAction.ALL_IN)

	return actions

## Get player info string
func get_info_string() -> String:
	return "%s (Seat %d): %d chips, State: %s" % [player_name, seat_position, chips, _state_to_string()]

func _state_to_string() -> String:
	match state:
		PlayerState.WAITING:
			return "Waiting"
		PlayerState.ACTIVE:
			return "Active"
		PlayerState.FOLDED:
			return "Folded"
		PlayerState.ALL_IN:
			return "All-in"
		PlayerState.SITTING_OUT:
			return "Sitting Out"
		_:
			return "Unknown"