class_name Poker<PERSON>ard
extends Card

## A specialized card class for poker games that extends the base Card framework
## Adds poker-specific functionality like suit and rank handling

enum Suit {
	CLUBS = 0,
	DIAMONDS = 1,
	HEARTS = 2,
	SPADES = 3
}

enum Rank {
	TWO = 2,
	THREE = 3,
	FOUR = 4,
	FIVE = 5,
	SIX = 6,
	SEVEN = 7,
	<PERSON>IGHT = 8,
	NINE = 9,
	TEN = 10,
	J<PERSON>K = 11,
	QUEEN = 12,
	KING = 13,
	ACE = 14
}

## The suit of the card (Clubs, Diamonds, Hearts, Spades)
@export var suit: Suit
## The rank of the card (2-14, where 11=<PERSON>, 12=<PERSON>, 13=<PERSON>, 14=<PERSON>)
@export var rank: Rank

## Initialize the poker card with suit and rank
func initialize_poker_card(card_suit: Suit, card_rank: Rank) -> void:
	suit = card_suit
	rank = card_rank
	_update_card_name()

## Update the card name based on suit and rank
func _update_card_name() -> void:
	var suit_name = _get_suit_name()
	var rank_name = _get_rank_name()
	card_name = suit_name + "_" + rank_name

## Get the string representation of the suit
func _get_suit_name() -> String:
	match suit:
		Suit.CLUBS:
			return "club"
		Suit.DIAMONDS:
			return "diamond"
		Suit.HEARTS:
			return "heart"
		Suit.SPADES:
			return "spade"
		_:
			return "unknown"

## Get the string representation of the rank
func _get_rank_name() -> String:
	match rank:
		Rank.TWO:
			return "2"
		Rank.THREE:
			return "3"
		Rank.FOUR:
			return "4"
		Rank.FIVE:
			return "5"
		Rank.SIX:
			return "6"
		Rank.SEVEN:
			return "7"
		Rank.EIGHT:
			return "8"
		Rank.NINE:
			return "9"
		Rank.TEN:
			return "10"
		Rank.JACK:
			return "J"
		Rank.QUEEN:
			return "Q"
		Rank.KING:
			return "K"
		Rank.ACE:
			return "A"
		_:
			return "unknown"

## Get the suit as a string for display
func get_suit_string() -> String:
	return _get_suit_name()

## Get the rank as a string for display
func get_rank_string() -> String:
	return _get_rank_name()

## Get the rank value for comparison (Ace high)
func get_rank_value() -> int:
	return rank

## Get the suit value for comparison
func get_suit_value() -> int:
	return suit

## Check if this card is red (Hearts or Diamonds)
func is_red() -> bool:
	return suit == Suit.HEARTS or suit == Suit.DIAMONDS

## Check if this card is black (Clubs or Spades)
func is_black() -> bool:
	return suit == Suit.CLUBS or suit == Suit.SPADES

## Compare cards by rank (for sorting)
func compare_by_rank(other_card: PokerCard) -> int:
	if rank < other_card.rank:
		return -1
	elif rank > other_card.rank:
		return 1
	else:
		return 0

## Compare cards by suit (for sorting)
func compare_by_suit(other_card: PokerCard) -> int:
	if suit < other_card.suit:
		return -1
	elif suit > other_card.suit:
		return 1
	else:
		return 0

## Get a unique identifier for this card (for hashing and comparison)
func get_card_id() -> int:
	return suit * 13 + (rank - 2)

## Create a string representation for debugging
func to_string() -> String:
	return get_rank_string() + " of " + get_suit_string().capitalize() + "s"

## Override the base get_string method
func get_string() -> String:
	return to_string()