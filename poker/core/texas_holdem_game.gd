class_name TexasHoldemGame
extends RefCounted

## Main game controller for Texas Hold'em poker
## Manages game flow, betting rounds, and player actions

enum GamePhase {
	WAITING = 0,      # Waiting for players
	PRE_FLOP = 1,     # Hole cards dealt, pre-flop betting
	FLOP = 2,         # First 3 community cards, betting round
	TURN = 3,         # 4th community card, betting round
	RIVER = 4,        # 5th community card, betting round
	SHOWDOWN = 5,     # Reveal hands and determine winner
	HAND_COMPLETE = 6 # Hand finished, preparing for next
}

## Game settings
@export var small_blind: int = 10
@export var big_blind: int = 20
@export var max_players: int = 9
@export var min_players: int = 2

## Game state
var current_phase: GamePhase = GamePhase.WAITING
var players: Array[PokerPlayer] = []
var active_players: Array[PokerPlayer] = []
var deck: PokerDeck
var community_cards: Array[PokerCard] = []
var pot: int = 0
var side_pots: Array = []  # For all-in situations

## Position tracking
var dealer_position: int = 0
var small_blind_position: int = 0
var big_blind_position: int = 0
var current_player_index: int = 0
var current_bet: int = 0
var min_raise: int = 0

## Round tracking
var betting_round_complete: bool = false
var hand_number: int = 0

## Signals for UI updates
signal game_phase_changed(phase: GamePhase)
signal player_action_occurred(player: PokerPlayer, action: PokerPlayer.PlayerAction, amount: int)
signal community_cards_dealt(cards: Array[PokerCard])
signal pot_updated(new_pot: int)
signal hand_complete(winners: Array, winnings: Array)
signal game_message(message: String)

func _init():
	deck = PokerDeck.new()

## Add a player to the game
func add_player(player: PokerPlayer) -> bool:
	if players.size() >= max_players:
		return false

	player.seat_position = players.size()
	players.append(player)
	player.action_taken.connect(_on_player_action)

	game_message.emit("Player %s joined the game" % player.player_name)

	# Start game if we have enough players
	if players.size() >= min_players and current_phase == GamePhase.WAITING:
		start_new_hand()

	return true

## Remove a player from the game
func remove_player(player: PokerPlayer) -> bool:
	var index = players.find(player)
	if index == -1:
		return false

	players.remove_at(index)

	# Update seat positions
	for i in range(players.size()):
		players[i].seat_position = i

	# Adjust dealer position if necessary
	if dealer_position >= players.size():
		dealer_position = 0

	game_message.emit("Player %s left the game" % player.player_name)
	return true

## Start a new hand
func start_new_hand() -> void:
	if players.size() < min_players:
		game_message.emit("Need at least %d players to start" % min_players)
		return

	hand_number += 1
	_reset_hand_state()
	_setup_positions()
	_deal_hole_cards()
	_post_blinds()
	_start_betting_round(GamePhase.PRE_FLOP)

## Reset state for new hand
func _reset_hand_state() -> void:
	community_cards.clear()
	pot = 0
	side_pots.clear()
	current_bet = 0
	min_raise = big_blind
	betting_round_complete = false

	# Reset all players for new hand
	active_players.clear()
	for player in players:
		player.reset_for_new_hand()
		if player.chips > 0:
			active_players.append(player)

## Setup dealer, small blind, and big blind positions
func _setup_positions() -> void:
	if players.size() < 2:
		return

	# Move dealer button
	dealer_position = (dealer_position + 1) % players.size()

	# Set positions based on number of players
	if players.size() == 2:
		# Heads-up: dealer is small blind
		small_blind_position = dealer_position
		big_blind_position = (dealer_position + 1) % players.size()
	else:
		# Multi-way: small blind is next to dealer
		small_blind_position = (dealer_position + 1) % players.size()
		big_blind_position = (dealer_position + 2) % players.size()

	# Set player flags
	for i in range(players.size()):
		players[i].is_dealer = (i == dealer_position)
		players[i].is_small_blind = (i == small_blind_position)
		players[i].is_big_blind = (i == big_blind_position)

## Deal hole cards to all active players
func _deal_hole_cards() -> void:
	deck.reset_and_shuffle()

	for player in active_players:
		var hole_cards = deck.deal_cards(2)
		player.deal_hole_cards(hole_cards)

	game_message.emit("Hole cards dealt to %d players" % active_players.size())

## Post small and big blinds
func _post_blinds() -> void:
	var sb_player = players[small_blind_position]
	var bb_player = players[big_blind_position]

	# Post small blind
	var sb_amount = min(small_blind, sb_player.chips)
	sb_player.perform_action(PokerPlayer.PlayerAction.CALL, sb_amount)
	pot += sb_amount

	# Post big blind
	var bb_amount = min(big_blind, bb_player.chips)
	bb_player.perform_action(PokerPlayer.PlayerAction.CALL, bb_amount)
	pot += bb_amount
	current_bet = bb_amount

	game_message.emit("Blinds posted: SB %d, BB %d" % [sb_amount, bb_amount])
	pot_updated.emit(pot)

## Start a betting round
func _start_betting_round(phase: GamePhase) -> void:
	current_phase = phase
	game_phase_changed.emit(phase)

	# Deal community cards based on phase
	match phase:
		GamePhase.FLOP:
			_deal_flop()
		GamePhase.TURN:
			_deal_turn()
		GamePhase.RIVER:
			_deal_river()

	# Reset betting for new round (except pre-flop)
	if phase != GamePhase.PRE_FLOP:
		current_bet = 0
		for player in active_players:
			player.reset_for_new_round()

	# Set first player to act
	_set_first_player_to_act(phase)

	# Start the action
	_continue_betting_round()

## Deal the flop (first 3 community cards)
func _deal_flop() -> void:
	deck.deal_card()  # Burn card
	for i in 3:
		community_cards.append(deck.deal_card())
	community_cards_dealt.emit(community_cards)
	game_message.emit("Flop dealt")

## Deal the turn (4th community card)
func _deal_turn() -> void:
	deck.deal_card()  # Burn card
	community_cards.append(deck.deal_card())
	community_cards_dealt.emit(community_cards)
	game_message.emit("Turn dealt")

## Deal the river (5th community card)
func _deal_river() -> void:
	deck.deal_card()  # Burn card
	community_cards.append(deck.deal_card())
	community_cards_dealt.emit(community_cards)
	game_message.emit("River dealt")

## Set the first player to act for a betting round
func _set_first_player_to_act(phase: GamePhase) -> void:
	if phase == GamePhase.PRE_FLOP:
		# Pre-flop: first to act is left of big blind
		current_player_index = (big_blind_position + 1) % players.size()
	else:
		# Post-flop: first to act is left of dealer
		current_player_index = (dealer_position + 1) % players.size()

	# Find next active player
	_find_next_active_player()

## Continue the betting round
func _continue_betting_round() -> void:
	# Check if betting round is complete
	if _is_betting_round_complete():
		_end_betting_round()
		return

	# Get current player
	var current_player = players[current_player_index]

	# Skip if player can't act
	if not current_player.can_act():
		_move_to_next_player()
		return

	# For human players, wait for input
	# For AI players, make decision automatically
	if not current_player.is_human:
		_make_ai_decision(current_player)
	else:
		game_message.emit("Waiting for %s to act" % current_player.player_name)

## Check if betting round is complete
func _is_betting_round_complete() -> bool:
	var active_count = 0
	var players_who_can_act = 0
	var all_bets_equal = true

	for player in active_players:
		if player.state == PokerPlayer.PlayerState.ACTIVE:
			active_count += 1
			if player.chips > 0:
				players_who_can_act += 1

			# Check if all bets are equal
			if player.current_bet != current_bet and player.chips > 0:
				all_bets_equal = false
		elif player.state == PokerPlayer.PlayerState.ALL_IN:
			active_count += 1

	# Round complete if only one player left or all bets equal and everyone acted
	return active_count <= 1 or (all_bets_equal and _all_players_have_acted())

## Check if all players have acted this round
func _all_players_have_acted() -> bool:
	for player in active_players:
		if player.state == PokerPlayer.PlayerState.ACTIVE and not player.has_acted_this_round:
			return false
	return true

## End the current betting round
func _end_betting_round() -> void:
	# Collect bets into pot
	for player in active_players:
		pot += player.current_bet
		player.current_bet = 0

	pot_updated.emit(pot)

	# Move to next phase
	match current_phase:
		GamePhase.PRE_FLOP:
			_start_betting_round(GamePhase.FLOP)
		GamePhase.FLOP:
			_start_betting_round(GamePhase.TURN)
		GamePhase.TURN:
			_start_betting_round(GamePhase.RIVER)
		GamePhase.RIVER:
			_start_showdown()

## Move to next player
func _move_to_next_player() -> void:
	current_player_index = (current_player_index + 1) % players.size()
	_find_next_active_player()
	_continue_betting_round()

## Find next active player who can act
func _find_next_active_player() -> void:
	var start_index = current_player_index
	while true:
		var player = players[current_player_index]
		if player.can_act():
			break

		current_player_index = (current_player_index + 1) % players.size()

		# Prevent infinite loop
		if current_player_index == start_index:
			break

## Handle player action
func _on_player_action(player: PokerPlayer, action: PokerPlayer.PlayerAction, amount: int) -> void:
	# Update game state based on action
	match action:
		PokerPlayer.PlayerAction.RAISE:
			current_bet = amount
			min_raise = amount - player.current_bet
		PokerPlayer.PlayerAction.CALL:
			# No change to current bet
			pass
		PokerPlayer.PlayerAction.ALL_IN:
			if amount > current_bet:
				current_bet = amount

	player_action_occurred.emit(player, action, amount)
	game_message.emit("%s %s %d" % [player.player_name, _action_to_string(action), amount])

	# Move to next player
	_move_to_next_player()

## Convert action to string for display
func _action_to_string(action: PokerPlayer.PlayerAction) -> String:
	match action:
		PokerPlayer.PlayerAction.FOLD:
			return "folds"
		PokerPlayer.PlayerAction.CHECK:
			return "checks"
		PokerPlayer.PlayerAction.CALL:
			return "calls"
		PokerPlayer.PlayerAction.RAISE:
			return "raises to"
		PokerPlayer.PlayerAction.ALL_IN:
			return "goes all-in for"
		_:
			return "unknown action"

## Start showdown phase
func _start_showdown() -> void:
	current_phase = GamePhase.SHOWDOWN
	game_phase_changed.emit(current_phase)

	# Evaluate all active players' hands
	var player_hands = []
	for player in active_players:
		if player.state != PokerPlayer.PlayerState.FOLDED:
			var all_cards = player.get_hole_cards() + community_cards
			var best_hand = HandEvaluator.evaluate_hand(all_cards)
			player_hands.append({
				"player": player,
				"hand": best_hand
			})

	# Sort by hand strength (best first)
	player_hands.sort_custom(func(a, b): return HandEvaluator.compare_hands(a.hand, b.hand) > 0)

	# Determine winners and distribute pot
	_distribute_pot(player_hands)

	# Complete the hand
	current_phase = GamePhase.HAND_COMPLETE
	game_phase_changed.emit(current_phase)

## Distribute pot to winners
func _distribute_pot(player_hands: Array) -> void:
	if player_hands.is_empty():
		return

	var winners = []
	var winnings = []

	# Find all players with the best hand (ties)
	var best_hand = player_hands[0].hand
	for player_hand in player_hands:
		if HandEvaluator.compare_hands(player_hand.hand, best_hand) == 0:
			winners.append(player_hand.player)

	# Split pot among winners
	var winnings_per_player = pot / winners.size()
	var remainder = pot % winners.size()

	for i in range(winners.size()):
		var player_winnings = winnings_per_player
		if i < remainder:
			player_winnings += 1  # Distribute remainder

		winners[i].add_chips(player_winnings)
		winnings.append(player_winnings)
		winners[i].hands_won += 1
		winners[i].total_winnings += player_winnings

	# Update statistics
	for player in active_players:
		player.hands_played += 1

	hand_complete.emit(winners, winnings)

	var winner_names = []
	for winner in winners:
		winner_names.append(winner.player_name)

	game_message.emit("Hand complete. Winners: %s" % ", ".join(winner_names))
	pot = 0
	pot_updated.emit(pot)

## Simple AI decision making (placeholder for more sophisticated AI)
func _make_ai_decision(player: PokerPlayer) -> void:
	var available_actions = player.get_available_actions(current_bet)

	if available_actions.is_empty():
		return

	# Simple random AI for now
	var action = available_actions[randi() % available_actions.size()]
	var amount = 0

	match action:
		PokerPlayer.PlayerAction.CALL:
			amount = current_bet
		PokerPlayer.PlayerAction.RAISE:
			amount = current_bet + min_raise
		PokerPlayer.PlayerAction.ALL_IN:
			amount = player.current_bet + player.chips

	player.perform_action(action, amount)

## Get current game state for UI
func get_game_state() -> Dictionary:
	return {
		"phase": current_phase,
		"pot": pot,
		"current_bet": current_bet,
		"community_cards": community_cards,
		"players": players,
		"active_players": active_players,
		"current_player": players[current_player_index] if current_player_index < players.size() else null,
		"dealer_position": dealer_position,
		"hand_number": hand_number
	}

## Force a player action (for UI input)
func force_player_action(player: PokerPlayer, action: PokerPlayer.PlayerAction, amount: int = 0) -> bool:
	if player != players[current_player_index]:
		return false

	if not player.can_act():
		return false

	return player.perform_action(action, amount)

## Get available actions for current player
func get_current_player_actions() -> Array[PokerPlayer.PlayerAction]:
	if current_player_index >= players.size():
		return []

	var current_player = players[current_player_index]
	return current_player.get_available_actions(current_bet)

## Check if game can start
func can_start_game() -> bool:
	return players.size() >= min_players

## Get game info string
func get_game_info() -> String:
	return "Hand #%d, Phase: %s, Pot: %d, Players: %d" % [
		hand_number,
		_phase_to_string(current_phase),
		pot,
		players.size()
	]

func _phase_to_string(phase: GamePhase) -> String:
	match phase:
		GamePhase.WAITING:
			return "Waiting"
		GamePhase.PRE_FLOP:
			return "Pre-Flop"
		GamePhase.FLOP:
			return "Flop"
		GamePhase.TURN:
			return "Turn"
		GamePhase.RIVER:
			return "River"
		GamePhase.SHOWDOWN:
			return "Showdown"
		GamePhase.HAND_COMPLETE:
			return "Hand Complete"
		_:
			return "Unknown"