extends Control

## Test scene for the Texas Hold'em poker game
## Demonstrates the core poker functionality

var poker_game: TexasHoldemGame
var players: Array[PokerPlayer] = []

@onready var game_info_label: Label = $VBoxContainer/GameInfoLabel
@onready var pot_label: Label = $VBoxContainer/PotLabel
@onready var phase_label: Label = $VBoxContainer/PhaseLabel
@onready var message_label: Label = $VBoxContainer/MessageLabel
@onready var players_container: VBoxContainer = $VBoxContainer/PlayersContainer
@onready var actions_container: HBoxContainer = $VBoxContainer/ActionsContainer
@onready var community_cards_label: Label = $VBoxContainer/CommunityCardsLabel

func _ready():
	_setup_poker_game()
	_create_test_players()
	_connect_signals()
	_update_ui()

## Setup the poker game
func _setup_poker_game() -> void:
	poker_game = TexasHoldemGame.new()
	poker_game.small_blind = 5
	poker_game.big_blind = 10

## Create test players
func _create_test_players() -> void:
	# Create human player
	var human_player = PokerPlayer.new(1, "Human Player", 1000)
	human_player.is_human = true
	players.append(human_player)
	poker_game.add_player(human_player)

	# Create AI players
	for i in range(3):
		var ai_player = PokerPlayer.new(i + 2, "AI Player %d" % (i + 1), 1000)
		ai_player.is_human = false
		players.append(ai_player)
		poker_game.add_player(ai_player)

## Connect game signals
func _connect_signals() -> void:
	poker_game.game_phase_changed.connect(_on_game_phase_changed)
	poker_game.player_action_occurred.connect(_on_player_action_occurred)
	poker_game.community_cards_dealt.connect(_on_community_cards_dealt)
	poker_game.pot_updated.connect(_on_pot_updated)
	poker_game.hand_complete.connect(_on_hand_complete)
	poker_game.game_message.connect(_on_game_message)

## Update the UI with current game state
func _update_ui() -> void:
	var game_state = poker_game.get_game_state()

	# Update labels
	game_info_label.text = poker_game.get_game_info()
	pot_label.text = "Pot: %d" % game_state.pot
	phase_label.text = "Phase: %s" % _phase_to_string(game_state.phase)

	# Update community cards
	var community_text = "Community Cards: "
	for card in game_state.community_cards:
		community_text += card.to_string() + " "
	community_cards_label.text = community_text

	# Update players info
	_update_players_display()

	# Update action buttons
	_update_action_buttons()

## Update players display
func _update_players_display() -> void:
	# Clear existing player labels
	for child in players_container.get_children():
		child.queue_free()

	# Add player info labels
	for player in players:
		var player_label = Label.new()
		var status = ""
		if player.is_dealer:
			status += "[D] "
		if player.is_small_blind:
			status += "[SB] "
		if player.is_big_blind:
			status += "[BB] "

		player_label.text = "%s%s: %d chips, Bet: %d, State: %s" % [
			status,
			player.player_name,
			player.chips,
			player.current_bet,
			_player_state_to_string(player.state)
		]

		# Highlight current player
		var current_player = poker_game.get_game_state().current_player
		if current_player and player == current_player:
			player_label.modulate = Color.YELLOW

		players_container.add_child(player_label)

## Update action buttons for human player
func _update_action_buttons() -> void:
	# Clear existing buttons
	for child in actions_container.get_children():
		child.queue_free()

	var current_player = poker_game.get_game_state().current_player
	if not current_player or not current_player.is_human:
		return

	var available_actions = poker_game.get_current_player_actions()

	for action in available_actions:
		var button = Button.new()
		button.text = _action_to_string(action)
		button.pressed.connect(_on_action_button_pressed.bind(action))
		actions_container.add_child(button)

## Handle action button press
func _on_action_button_pressed(action: PokerPlayer.PlayerAction) -> void:
	var current_player = poker_game.get_game_state().current_player
	if not current_player:
		return

	var amount = 0
	match action:
		PokerPlayer.PlayerAction.CALL:
			amount = poker_game.current_bet
		PokerPlayer.PlayerAction.RAISE:
			# Simple raise: current bet + big blind
			amount = poker_game.current_bet + poker_game.big_blind
		PokerPlayer.PlayerAction.ALL_IN:
			amount = current_player.current_bet + current_player.chips

	poker_game.force_player_action(current_player, action, amount)

## Signal handlers
func _on_game_phase_changed(phase: TexasHoldemGame.GamePhase) -> void:
	_update_ui()

func _on_player_action_occurred(player: PokerPlayer, action: PokerPlayer.PlayerAction, amount: int) -> void:
	_update_ui()

func _on_community_cards_dealt(cards: Array[PokerCard]) -> void:
	_update_ui()

func _on_pot_updated(new_pot: int) -> void:
	_update_ui()

func _on_hand_complete(winners: Array, winnings: Array) -> void:
	_update_ui()

	# Auto-start next hand after a delay
	await get_tree().create_timer(3.0).timeout
	poker_game.start_new_hand()

func _on_game_message(message: String) -> void:
	message_label.text = message
	_update_ui()

## Helper functions
func _phase_to_string(phase: TexasHoldemGame.GamePhase) -> String:
	match phase:
		TexasHoldemGame.GamePhase.WAITING:
			return "Waiting"
		TexasHoldemGame.GamePhase.PRE_FLOP:
			return "Pre-Flop"
		TexasHoldemGame.GamePhase.FLOP:
			return "Flop"
		TexasHoldemGame.GamePhase.TURN:
			return "Turn"
		TexasHoldemGame.GamePhase.RIVER:
			return "River"
		TexasHoldemGame.GamePhase.SHOWDOWN:
			return "Showdown"
		TexasHoldemGame.GamePhase.HAND_COMPLETE:
			return "Hand Complete"
		_:
			return "Unknown"

func _player_state_to_string(state: PokerPlayer.PlayerState) -> String:
	match state:
		PokerPlayer.PlayerState.WAITING:
			return "Waiting"
		PokerPlayer.PlayerState.ACTIVE:
			return "Active"
		PokerPlayer.PlayerState.FOLDED:
			return "Folded"
		PokerPlayer.PlayerState.ALL_IN:
			return "All-in"
		PokerPlayer.PlayerState.SITTING_OUT:
			return "Sitting Out"
		_:
			return "Unknown"

func _action_to_string(action: PokerPlayer.PlayerAction) -> String:
	match action:
		PokerPlayer.PlayerAction.FOLD:
			return "Fold"
		PokerPlayer.PlayerAction.CHECK:
			return "Check"
		PokerPlayer.PlayerAction.CALL:
			return "Call"
		PokerPlayer.PlayerAction.RAISE:
			return "Raise"
		PokerPlayer.PlayerAction.ALL_IN:
			return "All-In"
		_:
			return "Unknown"