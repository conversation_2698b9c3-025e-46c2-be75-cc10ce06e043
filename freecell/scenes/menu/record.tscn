[gd_scene format=3 uid="uid://bbvauwyj8ycxu"]

[node name="Record" type="HBoxContainer"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_right = -1192.0
offset_bottom = -1040.0
grow_horizontal = 2
grow_vertical = 2

[node name="MarginContainer" type="MarginContainer" parent="."]
custom_minimum_size = Vector2(50, 0)
layout_mode = 2

[node name="Date" type="Label" parent="."]
custom_minimum_size = Vector2(200, 25)
layout_mode = 2
text = "Date"

[node name="Result" type="Label" parent="."]
custom_minimum_size = Vector2(70, 0)
layout_mode = 2
text = "Result"

[node name="Seed" type="Label" parent="."]
custom_minimum_size = Vector2(150, 0)
layout_mode = 2
text = "Seed"

[node name="Score" type="Label" parent="."]
custom_minimum_size = Vector2(70, 0)
layout_mode = 2
text = "Score"

[node name="Move" type="Label" parent="."]
custom_minimum_size = Vector2(70, 0)
layout_mode = 2
text = "Move"

[node name="Undo" type="Label" parent="."]
custom_minimum_size = Vector2(70, 0)
layout_mode = 2
text = "Undo"

[node name="Time" type="Label" parent="."]
custom_minimum_size = Vector2(70, 0)
layout_mode = 2
text = "Time"

[node name="MarginContainer2" type="MarginContainer" parent="."]
custom_minimum_size = Vector2(50, 0)
layout_mode = 2
