[gd_scene load_steps=19 format=3 uid="uid://4sjnrl4pid6b"]

[ext_resource type="PackedScene" uid="uid://c7u8hryloq7hy" path="res://addons/card-framework/card_manager.tscn" id="1_kh5ya"]
[ext_resource type="Script" uid="uid://dfipwybqb13ee" path="res://freecell/scenes/main_game/freecell_game.gd" id="1_qroca"]
[ext_resource type="PackedScene" uid="uid://dghhf8lu7oopx" path="res://freecell/scenes/card_container/freecell.tscn" id="2_3jyru"]
[ext_resource type="PackedScene" uid="uid://c644xfredmjoj" path="res://freecell/scenes/card_factory/frecell_card_factory.tscn" id="4_hp8si"]
[ext_resource type="PackedScene" uid="uid://d134c8yu6d6qv" path="res://freecell/scenes/card_container/foundation.tscn" id="4_ivlr5"]
[ext_resource type="Texture2D" uid="uid://ciq4unq0c1ema" path="res://freecell/assets/images/spots/foundation_heart_spot.png" id="7_epxn1"]
[ext_resource type="PackedScene" uid="uid://bn72g6fewh8aq" path="res://freecell/scenes/card_container/tableau.tscn" id="7_wmb6j"]
[ext_resource type="PackedScene" uid="uid://dxmui4fuxab8t" path="res://freecell/scenes/main_game/game_generator.tscn" id="8_flq1e"]
[ext_resource type="Texture2D" uid="uid://cl7jpb0rqwpjq" path="res://freecell/assets/images/spots/foundation_spade_spot.png" id="8_neli8"]
[ext_resource type="Texture2D" uid="uid://cs060bvtns8hy" path="res://freecell/assets/images/spots/foundation_diamond_spot.png" id="9_mghkt"]
[ext_resource type="Texture2D" uid="uid://c7x2lxmfwbnfc" path="res://freecell/assets/images/spots/foundation_club_spot.png" id="10_lka7w"]
[ext_resource type="PackedScene" uid="uid://dk6rb7lhv1ef6" path="res://addons/card-framework/pile.tscn" id="12_yh0vm"]
[ext_resource type="PackedScene" uid="uid://cke08pyglaa48" path="res://freecell/scenes/dialog/restart_game_dialog.tscn" id="14_674kq"]
[ext_resource type="PackedScene" uid="uid://ddx72jvfinglm" path="res://freecell/scenes/dialog/go_to_menu_dialog.tscn" id="15_2mski"]
[ext_resource type="PackedScene" uid="uid://br04c2rylm2m1" path="res://freecell/scenes/dialog/result_dialog.tscn" id="16_k3qx6"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_5g7xq"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_0xr5u"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_l6lc3"]

[node name="FreecellGame" type="Node"]
script = ExtResource("1_qroca")

[node name="TimeLiteral" type="RichTextLabel" parent="."]
offset_left = 924.0
offset_top = 34.0
offset_right = 964.0
offset_bottom = 74.0
bbcode_enabled = true
text = "[center]Time[/center]"
scroll_active = false

[node name="Time" type="Button" parent="."]
offset_left = 905.0
offset_top = 58.0
offset_right = 980.0
offset_bottom = 98.0
theme_override_font_sizes/font_size = 23
button_mask = 0
text = "0
"

[node name="ScoreLiteral" type="RichTextLabel" parent="."]
offset_left = 1002.0
offset_top = 34.0
offset_right = 1052.0
offset_bottom = 74.0
bbcode_enabled = true
text = "[center]Score[/center]"
scroll_active = false

[node name="Score" type="Button" parent="."]
offset_left = 990.0
offset_top = 58.0
offset_right = 1065.0
offset_bottom = 98.0
theme_override_font_sizes/font_size = 23
button_mask = 0
text = "0"

[node name="ButtonRestartGame" type="Button" parent="."]
offset_left = 905.0
offset_top = 107.0
offset_right = 1065.0
offset_bottom = 147.0
text = "Restart"

[node name="ButtonUndo" type="Button" parent="."]
offset_left = 905.0
offset_top = 164.0
offset_right = 1065.0
offset_bottom = 204.0
text = "Undo"

[node name="ButtonMenu" type="Button" parent="."]
offset_left = 905.0
offset_top = 221.0
offset_right = 1065.0
offset_bottom = 261.0
text = "Menu"

[node name="CardManager" parent="." instance=ExtResource("1_kh5ya")]
card_factory_scene = ExtResource("4_hp8si")

[node name="Freecell_1" parent="CardManager" instance=ExtResource("2_3jyru")]
offset_left = 100.0
offset_top = 50.0
offset_right = 100.0
offset_bottom = 50.0

[node name="Freecell_2" parent="CardManager" instance=ExtResource("2_3jyru")]
offset_left = 300.0
offset_top = 50.0
offset_right = 300.0
offset_bottom = 50.0

[node name="Freecell_3" parent="CardManager" instance=ExtResource("2_3jyru")]
offset_left = 500.0
offset_top = 50.0
offset_right = 500.0
offset_bottom = 50.0

[node name="Freecell_4" parent="CardManager" instance=ExtResource("2_3jyru")]
offset_left = 700.0
offset_top = 50.0
offset_right = 700.0
offset_bottom = 50.0

[node name="Foundation_Heart" parent="CardManager" instance=ExtResource("4_ivlr5")]
offset_left = 1120.0
offset_top = 50.0
offset_right = 1120.0
offset_bottom = 50.0
suit = 2
sensor_texture = ExtResource("7_epxn1")

[node name="Foundation_Spade" parent="CardManager" instance=ExtResource("4_ivlr5")]
offset_left = 1320.0
offset_top = 50.0
offset_right = 1320.0
offset_bottom = 50.0
suit = 1
sensor_texture = ExtResource("8_neli8")

[node name="Foundation_Diamond" parent="CardManager" instance=ExtResource("4_ivlr5")]
offset_left = 1520.0
offset_top = 50.0
offset_right = 1520.0
offset_bottom = 50.0
suit = 3
sensor_texture = ExtResource("9_mghkt")

[node name="Foundation_Club" parent="CardManager" instance=ExtResource("4_ivlr5")]
offset_left = 1720.0
offset_top = 50.0
offset_right = 1720.0
offset_bottom = 50.0
suit = 4
sensor_texture = ExtResource("10_lka7w")

[node name="Tableau_1" parent="CardManager" instance=ExtResource("7_wmb6j")]
offset_left = 100.0
offset_top = 300.0
offset_right = 100.0
offset_bottom = 300.0

[node name="Tableau_2" parent="CardManager" instance=ExtResource("7_wmb6j")]
offset_left = 331.0
offset_top = 300.0
offset_right = 331.0
offset_bottom = 300.0

[node name="Tableau_3" parent="CardManager" instance=ExtResource("7_wmb6j")]
offset_left = 562.0
offset_top = 300.0
offset_right = 562.0
offset_bottom = 300.0

[node name="Tableau_4" parent="CardManager" instance=ExtResource("7_wmb6j")]
offset_left = 793.0
offset_top = 300.0
offset_right = 793.0
offset_bottom = 300.0

[node name="Tableau_5" parent="CardManager" instance=ExtResource("7_wmb6j")]
offset_left = 1024.0
offset_top = 300.0
offset_right = 1024.0
offset_bottom = 300.0

[node name="Tableau_6" parent="CardManager" instance=ExtResource("7_wmb6j")]
offset_left = 1255.0
offset_top = 300.0
offset_right = 1255.0
offset_bottom = 300.0

[node name="Tableau_7" parent="CardManager" instance=ExtResource("7_wmb6j")]
offset_left = 1486.0
offset_top = 300.0
offset_right = 1486.0
offset_bottom = 300.0

[node name="Tableau_8" parent="CardManager" instance=ExtResource("7_wmb6j")]
offset_left = 1717.0
offset_top = 300.0
offset_right = 1717.0
offset_bottom = 300.0

[node name="StartPosition" parent="CardManager" instance=ExtResource("12_yh0vm")]
offset_left = 909.0
offset_top = 820.0
offset_right = 909.0
offset_bottom = 820.0
stack_display_gap = 0
max_stack_display = 1
allow_card_movement = false
restrict_to_top_card = false
align_drop_zone_with_top_card = false
enable_drop_zone = false

[node name="GameGenerator" parent="." instance=ExtResource("8_flq1e")]

[node name="RestartGameDialog" parent="." instance=ExtResource("14_674kq")]

[node name="GoToMenuDialog" parent="." instance=ExtResource("15_2mski")]

[node name="Information" type="TextEdit" parent="."]
offset_left = 20.0
offset_top = 1050.0
offset_right = 1920.0
offset_bottom = 1080.0
theme_override_styles/normal = SubResource("StyleBoxEmpty_5g7xq")
theme_override_styles/focus = SubResource("StyleBoxEmpty_0xr5u")
theme_override_styles/read_only = SubResource("StyleBoxEmpty_l6lc3")
editable = false
context_menu_enabled = false
shortcut_keys_enabled = false
deselect_on_focus_loss_enabled = false
drag_and_drop_selection_enabled = false
virtual_keyboard_enabled = false
middle_mouse_paste_enabled = false

[node name="ResultDialog" parent="." instance=ExtResource("16_k3qx6")]
