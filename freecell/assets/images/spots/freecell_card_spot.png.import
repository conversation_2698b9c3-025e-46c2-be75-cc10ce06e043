[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://dtflrea27nlrp"
path="res://.godot/imported/freecell_card_spot.png-2fa44f5b8ce797da5af08be6e85457e4.ctex"
metadata={
"vram_texture": false
}

[deps]

source_file="res://freecell/assets/images/spots/freecell_card_spot.png"
dest_files=["res://.godot/imported/freecell_card_spot.png-2fa44f5b8ce797da5af08be6e85457e4.ctex"]

[params]

compress/mode=0
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=false
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=1
