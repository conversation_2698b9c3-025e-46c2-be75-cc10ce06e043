[gd_scene load_steps=12 format=3 uid="uid://60bwveuu2rt"]

[ext_resource type="Script" uid="uid://cf56ce8uvkmha" path="res://example1/example1.gd" id="1_2r7vq"]
[ext_resource type="PackedScene" uid="uid://c7u8hryloq7hy" path="res://addons/card-framework/card_manager.tscn" id="1_rurpr"]
[ext_resource type="PackedScene" uid="uid://dk6rb7lhv1ef6" path="res://addons/card-framework/pile.tscn" id="2_xkfcc"]
[ext_resource type="PackedScene" uid="uid://diap3gdfbqvjv" path="res://example1/my_card_factory.tscn" id="3_l4bd6"]
[ext_resource type="PackedScene" uid="uid://bkpjlq7ggckg6" path="res://addons/card-framework/hand.tscn" id="3_xp5g3"]
[ext_resource type="Texture2D" uid="uid://cv6ecvmkml3ok" path="res://example1/assets/images/cards/cardBack_green1.png" id="5_kl5wo"]
[ext_resource type="Texture2D" uid="uid://dacwc4ros7wiv" path="res://example1/assets/images/cards/cardBack_green2.png" id="6_u8c1b"]
[ext_resource type="Texture2D" uid="uid://c8mcrcwmgi0oc" path="res://example1/assets/images/cards/cardBack_green3.png" id="8_cv7sm"]
[ext_resource type="Texture2D" uid="uid://cpkjxmuv1ho4b" path="res://example1/assets/images/cards/cardBack_red2.png" id="8_nwj6x"]
[ext_resource type="Texture2D" uid="uid://bdf2cukmms3nb" path="res://example1/assets/images/cards/cardBack_red3.png" id="10_tthv0"]
[ext_resource type="Texture2D" uid="uid://b1jy1scbarbg5" path="res://example1/assets/images/cards/cardBack_red4.png" id="11_5najx"]

[node name="Example1" type="Node"]
script = ExtResource("1_2r7vq")

[node name="ButtonContainer" type="HBoxContainer" parent="."]
offset_left = 100.0
offset_top = 100.0
offset_right = 200.0
offset_bottom = 140.0

[node name="ButtonColumnContainer1" type="VBoxContainer" parent="ButtonContainer"]
layout_mode = 2

[node name="Draw1Button" type="Button" parent="ButtonContainer/ButtonColumnContainer1"]
layout_mode = 2
text = "draw 1"

[node name="Draw3Button" type="Button" parent="ButtonContainer/ButtonColumnContainer1"]
layout_mode = 2
text = "draw 3"

[node name="ResetDeckButton" type="Button" parent="ButtonContainer/ButtonColumnContainer1"]
layout_mode = 2
text = "reset deck"

[node name="UndoButton" type="Button" parent="ButtonContainer/ButtonColumnContainer1"]
layout_mode = 2
text = "undo"

[node name="ShuffleHandButton" type="Button" parent="ButtonContainer/ButtonColumnContainer1"]
layout_mode = 2
text = "shuffle hand"

[node name="Discard1Button" type="Button" parent="ButtonContainer/ButtonColumnContainer1"]
layout_mode = 2
text = "discard 1"

[node name="Discard3Button" type="Button" parent="ButtonContainer/ButtonColumnContainer1"]
layout_mode = 2
text = "discard 3"

[node name="ButtonColumnContainer2" type="VBoxContainer" parent="ButtonContainer"]
layout_mode = 2

[node name="MoveToPile1Button" type="Button" parent="ButtonContainer/ButtonColumnContainer2"]
layout_mode = 2
text = "move to pile1"

[node name="MoveToPile2Button" type="Button" parent="ButtonContainer/ButtonColumnContainer2"]
layout_mode = 2
text = "move to pile2"

[node name="MoveToPile3Button" type="Button" parent="ButtonContainer/ButtonColumnContainer2"]
layout_mode = 2
text = "move to pile3"

[node name="MoveToPile4Button" type="Button" parent="ButtonContainer/ButtonColumnContainer2"]
layout_mode = 2
text = "move to pile4"

[node name="ClearAllButton" type="Button" parent="ButtonContainer/ButtonColumnContainer2"]
layout_mode = 2
text = "clear all"

[node name="Draw3AtFront" type="Button" parent="ButtonContainer/ButtonColumnContainer2"]
layout_mode = 2
text = "draw 3 at front"

[node name="CardManager" parent="." instance=ExtResource("1_rurpr")]
card_factory_scene = ExtResource("3_l4bd6")

[node name="Pile1" parent="CardManager" instance=ExtResource("2_xkfcc")]
offset_left = 1000.0
offset_top = 100.0
offset_right = 1000.0
offset_bottom = 100.0
sensor_texture = ExtResource("5_kl5wo")
sensor_visibility = true

[node name="Pile2" parent="CardManager" instance=ExtResource("2_xkfcc")]
offset_left = 1200.0
offset_top = 100.0
offset_right = 1200.0
offset_bottom = 100.0
card_face_up = false
layout = 3
sensor_texture = ExtResource("6_u8c1b")
sensor_visibility = true

[node name="Pile3" parent="CardManager" instance=ExtResource("2_xkfcc")]
offset_left = 1400.0
offset_top = 100.0
offset_right = 1400.0
offset_bottom = 100.0
stack_display_gap = 40
layout = 1
restrict_to_top_card = false
sensor_texture = ExtResource("8_cv7sm")
sensor_visibility = true

[node name="Pile4" parent="CardManager" instance=ExtResource("2_xkfcc")]
offset_left = 1600.0
offset_top = 100.0
offset_right = 1600.0
offset_bottom = 100.0
layout = 2
allow_card_movement = false
sensor_texture = ExtResource("8_nwj6x")
sensor_visibility = true

[node name="Hand" parent="CardManager" instance=ExtResource("3_xp5g3")]
offset_left = 1000.0
offset_top = 800.0
offset_right = 1000.0
offset_bottom = 800.0

[node name="Deck" parent="CardManager" instance=ExtResource("2_xkfcc")]
offset_left = 300.0
offset_top = 800.0
offset_right = 300.0
offset_bottom = 800.0
card_face_up = false
sensor_texture = ExtResource("10_tthv0")

[node name="Discard" parent="CardManager" instance=ExtResource("2_xkfcc")]
offset_left = 1600.0
offset_top = 800.0
offset_right = 1600.0
offset_bottom = 800.0
sensor_texture = ExtResource("11_5najx")

[node name="Pile1Label" type="Label" parent="."]
offset_left = 1055.0
offset_top = 50.0
offset_right = 1097.0
offset_bottom = 73.0
text = "Pile 1"

[node name="Pile2Label" type="Label" parent="."]
offset_left = 1255.0
offset_top = 50.0
offset_right = 1297.0
offset_bottom = 73.0
text = "Pile 2"

[node name="Pile3Label" type="Label" parent="."]
offset_left = 1455.0
offset_top = 50.0
offset_right = 1497.0
offset_bottom = 73.0
text = "Pile 3"

[node name="Pile4Label" type="Label" parent="."]
offset_left = 1655.0
offset_top = 50.0
offset_right = 1697.0
offset_bottom = 73.0
text = "Pile 4"

[node name="HandLabel" type="Label" parent="."]
offset_left = 1050.0
offset_top = 700.0
offset_right = 1092.0
offset_bottom = 723.0
text = "Hand"

[node name="DeckLabel" type="Label" parent="."]
offset_left = 355.0
offset_top = 700.0
offset_right = 395.0
offset_bottom = 723.0
text = "Deck"

[node name="DiscardLabel" type="Label" parent="."]
offset_left = 1655.0
offset_top = 700.0
offset_right = 1714.0
offset_bottom = 723.0
text = "Discard"

[connection signal="pressed" from="ButtonContainer/ButtonColumnContainer1/Draw1Button" to="." method="_on_draw_1_button_pressed"]
[connection signal="pressed" from="ButtonContainer/ButtonColumnContainer1/Draw3Button" to="." method="_on_draw_3_button_pressed"]
[connection signal="pressed" from="ButtonContainer/ButtonColumnContainer1/ResetDeckButton" to="." method="_on_reset_deck_button_pressed"]
[connection signal="pressed" from="ButtonContainer/ButtonColumnContainer1/UndoButton" to="." method="_on_undo_button_pressed"]
[connection signal="pressed" from="ButtonContainer/ButtonColumnContainer1/ShuffleHandButton" to="." method="_on_shuffle_hand_button_pressed"]
[connection signal="pressed" from="ButtonContainer/ButtonColumnContainer1/Discard1Button" to="." method="_on_discard_1_button_pressed"]
[connection signal="pressed" from="ButtonContainer/ButtonColumnContainer1/Discard3Button" to="." method="_on_discard_3_button_pressed"]
[connection signal="pressed" from="ButtonContainer/ButtonColumnContainer2/MoveToPile1Button" to="." method="_on_move_to_pile_1_button_pressed"]
[connection signal="pressed" from="ButtonContainer/ButtonColumnContainer2/MoveToPile2Button" to="." method="_on_move_to_pile_2_button_pressed"]
[connection signal="pressed" from="ButtonContainer/ButtonColumnContainer2/MoveToPile3Button" to="." method="_on_move_to_pile_3_button_pressed"]
[connection signal="pressed" from="ButtonContainer/ButtonColumnContainer2/MoveToPile4Button" to="." method="_on_move_to_pile_4_button_pressed"]
[connection signal="pressed" from="ButtonContainer/ButtonColumnContainer2/ClearAllButton" to="." method="_on_clear_all_button_pressed"]
[connection signal="pressed" from="ButtonContainer/ButtonColumnContainer2/Draw3AtFront" to="." method="_on_draw_3_at_front_button_pressed"]
